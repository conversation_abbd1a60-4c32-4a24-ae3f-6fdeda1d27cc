import React, { useEffect, useRef, useState } from 'react';

interface ElsaStudioEmbedProps {
  height?: string;
  className?: string;
}

export const ElsaStudioEmbed: React.FC<ElsaStudioEmbedProps> = ({
  height = 'calc(100vh - 120px)',
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadElsaStudio = async () => {
      try {
        // Load Elsa Studio WASM directly into this container
        const script = document.createElement('script');
        script.src = process.env.NODE_ENV === 'development' 
          ? 'http://localhost:6001/_framework/blazor.webassembly.js'
          : '/elsa-studio/_framework/blazor.webassembly.js';
        
        script.onload = () => {
          setIsLoading(false);
        };
        
        script.onerror = () => {
          setError('Failed to load Elsa Studio. Please ensure it is running.');
          setIsLoading(false);
        };
        
        document.head.appendChild(script);

        // Initialize Blazor in our container
        if (containerRef.current) {
          containerRef.current.innerHTML = '<div id="elsa-app">Loading Elsa Studio...</div>';
        }

        // Wait for Blazor to be available and start it
        const checkBlazor = () => {
          if ((window as any).Blazor) {
            (window as any).Blazor.start({
              applicationParameters: {
                apiUrl: window.location.origin + '/elsa/api'
              }
            });
          } else {
            setTimeout(checkBlazor, 100);
          }
        };
        
        checkBlazor();

      } catch (err) {
        setError('Failed to initialize Elsa Studio');
        setIsLoading(false);
      }
    };

    loadElsaStudio();
  }, []);

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️ Elsa Studio Error</div>
          <div className="text-red-700 font-medium">{error}</div>
          <div className="text-sm text-red-600 mt-2">
            Make sure Elsa Studio is running: <code>dotnet run --project src/Harmoni360.ElsaStudio</code>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`elsa-studio-embed ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Elsa Studio...</p>
          </div>
        </div>
      )}
      
      <div 
        ref={containerRef}
        style={{ 
          height,
          width: '100%',
          display: isLoading ? 'none' : 'block'
        }}
      />
    </div>
  );
};

export default ElsaStudioEmbed;