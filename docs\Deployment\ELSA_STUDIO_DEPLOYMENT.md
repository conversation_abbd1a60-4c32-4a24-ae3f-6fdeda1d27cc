# Elsa Studio Deployment Guide

## Development vs Production Configuration

### Development (Current Setup)
- React App: `http://localhost:5173`
- Backend API: `https://localhost:5000`
- Elsa Studio: `https://localhost:6001`

### Production Deployment Options

## Option 1: Dual-Container Deployment (Recommended)

### Docker Compose Configuration
```yaml
version: '3.8'
services:
  harmoni360-app:
    build: 
      context: .
      dockerfile: src/Harmoni360.Web/Dockerfile
    ports:
      - "80:80"
      - "443:443"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ELSA_STUDIO_URL=https://elsa-studio:443
    depends_on:
      - postgres
      - elsa-studio

  elsa-studio:
    build:
      context: .
      dockerfile: src/Harmoni360.ElsaStudio/Dockerfile
    ports:
      - "6001:80"
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ELSA_API_URL=https://harmoni360-app:443/elsa/api

  postgres:
    image: postgres:15
    environment:
      - POSTGRES_DB=harmoni360
    volumes:
      - postgres_data:/var/lib/postgresql/data

volumes:
  postgres_data:
```

## Option 2: Fly.io Multi-App Deployment

### Primary App (Harmoni360)
```toml
# fly.toml
app = "harmoni360-hse"

[build]
  dockerfile = "src/Harmoni360.Web/Dockerfile"

[[services]]
  internal_port = 80
  protocol = "tcp"

  [[services.ports]]
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ELSA_STUDIO_URL = "https://harmoni360-elsa-studio.fly.dev"
```

### Elsa Studio App
```toml
# elsa-studio-fly.toml
app = "harmoni360-elsa-studio"

[build]
  dockerfile = "src/Harmoni360.ElsaStudio/Dockerfile"

[[services]]
  internal_port = 80
  protocol = "tcp"

  [[services.ports]]
    handlers = ["http"]
    port = 80

  [[services.ports]]
    handlers = ["tls", "http"]
    port = 443

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ELSA_API_URL = "https://harmoni360-hse.fly.dev/elsa/api"
```

## Environment Variables Required

### React App (.env.production)
```
REACT_APP_ELSA_STUDIO_URL=https://your-elsa-studio-domain.com
REACT_APP_API_BASE_URL=https://your-api-domain.com
```

### Elsa Studio
```
ELSA_API_URL=https://your-api-domain.com/elsa/api
ASPNETCORE_URLS=http://+:80
```

### Main API
```
ELSA_STUDIO_URL=https://your-elsa-studio-domain.com
CORS_ORIGINS=https://your-frontend-domain.com,https://your-elsa-studio-domain.com
```

## Deployment Commands

### Fly.io Deployment
```bash
# Deploy main app
fly deploy

# Deploy Elsa Studio (separate app)
fly deploy -c elsa-studio-fly.toml

# Set secrets
fly secrets set JWT_KEY="your-jwt-key"
fly secrets set CONNECTION_STRING="your-db-connection"
```

### Docker Deployment
```bash
# Build and run with docker-compose
docker-compose up -d

# Or build individually
docker build -f src/Harmoni360.Web/Dockerfile -t harmoni360-app .
docker build -f src/Harmoni360.ElsaStudio/Dockerfile -t elsa-studio .
```

## Important Production Considerations

1. **SSL/TLS**: Both apps need valid certificates
2. **CORS**: Update origins for production domains
3. **Authentication**: Ensure JWT tokens work across domains
4. **Database**: Shared PostgreSQL instance for both apps
5. **Monitoring**: Set up health checks for both services

## Security Notes

- Elsa Studio should be behind authentication
- API endpoints need proper authorization
- Use HTTPS in production
- Implement proper CORS policies
- Consider VPN or private networking for Elsa Studio access