using Microsoft.AspNetCore.Mvc;

namespace Harmoni360.Web.Controllers;

[ApiController]
[Route("elsa/api/features")]
public class ElsaFeaturesController : ControllerBase
{
    [HttpGet("installed")]
    public IActionResult GetInstalledFeatures()
    {
        // Return response in the format Elsa Studio expects
        var response = new
        {
            items = new[]
            {
                new
                {
                    name = "Elsa.WorkflowManagement",
                    displayName = "Workflow Management",
                    description = "Provides workflow management capabilities",
                    category = "Workflows",
                    isEnabled = true,
                    version = "3.4.2"
                },
                new
                {
                    name = "Elsa.WorkflowRuntime", 
                    displayName = "Workflow Runtime",
                    description = "Provides workflow runtime capabilities",
                    category = "Runtime",
                    isEnabled = true,
                    version = "3.4.2"
                },
                new
                {
                    name = "Elsa.Identity",
                    displayName = "Identity",
                    description = "Provides identity and authentication features",
                    category = "Security",
                    isEnabled = true,
                    version = "3.4.2"
                },
                new
                {
                    name = "Elsa.Studio.Dashboard",
                    displayName = "Dashboard",
                    description = "Provides dashboard capabilities",
                    category = "UI",
                    isEnabled = true,
                    version = "3.4.2"
                }
            },
            totalCount = 4
        };

        return Ok(response);
    }
}