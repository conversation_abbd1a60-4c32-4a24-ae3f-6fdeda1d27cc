import React, { useRef, useEffect } from 'react';
import { useElsaComponent } from '../../hooks/useElsaComponent';

interface WorkflowDashboardComponentProps {
  className?: string;
  onReady?: () => void;
  onError?: (error: Error) => void;
}

export const WorkflowDashboardComponent: React.FC<WorkflowDashboardComponentProps> = ({
  className = '',
  onReady,
  onError
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const containerId = `workflow-dashboard-${Math.random().toString(36).substr(2, 9)}`;

  const { isLoading, isReady, error } = useElsaComponent({
    componentType: 'dashboard',
    containerId,
    onReady,
    onError
  });

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.id = containerId;
    }
  }, [containerId]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️ Workflow Dashboard Error</div>
          <div className="text-red-700 font-medium">{error}</div>
          <div className="text-sm text-red-600 mt-2">
            Please ensure Elsa Studio is properly configured and running.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`workflow-dashboard-container ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Workflow Dashboard...</p>
          </div>
        </div>
      )}
      
      <div 
        ref={containerRef}
        className={`workflow-dashboard-host ${isReady ? 'ready' : 'loading'}`}
        style={{
          width: '100%',
          minHeight: '600px',
          display: isLoading ? 'none' : 'block'
        }}
      />
    </div>
  );
};

export default WorkflowDashboardComponent;