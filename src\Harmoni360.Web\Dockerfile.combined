# Combined Dockerfile for both applications
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# Copy project files
COPY ["src/Harmoni360.Web/Harmoni360.Web.csproj", "src/Harmoni360.Web/"]
COPY ["src/Harmoni360.ElsaStudio/Harmoni360.ElsaStudio.csproj", "src/Harmoni360.ElsaStudio/"]
COPY ["src/Harmoni360.Application/Harmoni360.Application.csproj", "src/Harmoni360.Application/"]
COPY ["src/Harmoni360.Domain/Harmoni360.Domain.csproj", "src/Harmoni360.Domain/"]
COPY ["src/Harmoni360.Infrastructure/Harmoni360.Infrastructure.csproj", "src/Harmoni360.Infrastructure/"]

# Restore packages
RUN dotnet restore "src/Harmoni360.Web/Harmoni360.Web.csproj"
RUN dotnet restore "src/Harmoni360.ElsaStudio/Harmoni360.ElsaStudio.csproj"

# Copy source code
COPY . .

# Build main application
WORKDIR "/src/src/Harmoni360.Web"
RUN dotnet build "Harmoni360.Web.csproj" -c Release -o /app/build/main

# Build Elsa Studio
WORKDIR "/src/src/Harmoni360.ElsaStudio"
RUN dotnet build "Harmoni360.ElsaStudio.csproj" -c Release -o /app/build/elsa

# Publish both applications
FROM build AS publish
WORKDIR "/src/src/Harmoni360.Web"
RUN dotnet publish "Harmoni360.Web.csproj" -c Release -o /app/publish/main

WORKDIR "/src/src/Harmoni360.ElsaStudio"
RUN dotnet publish "Harmoni360.ElsaStudio.csproj" -c Release -o /app/publish/elsa

# Final stage
FROM base AS final
WORKDIR /app

# Copy both applications
COPY --from=publish /app/publish/main ./main
COPY --from=publish /app/publish/elsa ./elsa

# Install supervisor for multi-process management
RUN apt-get update && apt-get install -y supervisor

# Create supervisor configuration
RUN echo '[supervisord]' > /etc/supervisor/conf.d/supervisord.conf && \
    echo 'nodaemon=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '[program:harmoni360-main]' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'command=dotnet /app/main/Harmoni360.Web.dll --urls=http://0.0.0.0:8080' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'directory=/app/main' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stderr_logfile=/var/log/harmoni360-main.err.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stdout_logfile=/var/log/harmoni360-main.out.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo '[program:elsa-studio]' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'command=dotnet /app/elsa/Harmoni360.ElsaStudio.dll --urls=http://0.0.0.0:8081' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'directory=/app/elsa' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autostart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'autorestart=true' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stderr_logfile=/var/log/elsa-studio.err.log' >> /etc/supervisor/conf.d/supervisord.conf && \
    echo 'stdout_logfile=/var/log/elsa-studio.out.log' >> /etc/supervisor/conf.d/supervisord.conf

ENTRYPOINT ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/supervisord.conf"]