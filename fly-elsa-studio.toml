# Elsa Studio Application
app = "harmoni360-elsa-studio"
primary_region = "sjc"

[build]
  dockerfile = "src/Harmoni360.ElsaStudio/Dockerfile"

[http_service]
  internal_port = 8080
  force_https = true
  auto_stop_machines = true
  auto_start_machines = true
  min_machines_running = 0
  processes = ["app"]

[[vm]]
  cpu_kind = "shared"
  cpus = 1
  memory_mb = 512

[env]
  ASPNETCORE_ENVIRONMENT = "Production"
  ELSA_API_URL = "https://harmoni360-hse.fly.dev/elsa/api"

[[services]]
  protocol = "tcp"
  internal_port = 8080
  processes = ["app"]

  [[services.ports]]
    port = 80
    handlers = ["http"]
    force_https = true

  [[services.ports]]
    port = 443
    handlers = ["tls", "http"]

  [services.concurrency]
    type = "connections"
    hard_limit = 25
    soft_limit = 20

  [[services.tcp_checks]]
    interval = "15s"
    timeout = "2s"
    grace_period = "1s"