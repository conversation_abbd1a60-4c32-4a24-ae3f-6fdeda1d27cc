<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Harmoni360.ElsaStudio</title>
    <base href="/" />
    <link rel="stylesheet" href="css/bootstrap/bootstrap.min.css" />
    <link rel="stylesheet" href="css/app.css" />
    <link rel="icon" type="image/png" href="favicon.png" />
    <link href="Harmoni360.ElsaStudio.styles.css" rel="stylesheet" />
    
    <!-- Add MudBlazor JavaScript stubs to prevent errors -->
    <script>
        window.mudElementRef = {
            getBoundingClientRect: function() {
                return { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0, x: 0, y: 0 };
            }
        };
        
        // Mock MudBlazor functions to prevent errors
        window.mudKeyInterceptor = {
            connect: function() { },
            disconnect: function() { }
        };
        
        window.mudScrollManager = {
            lockScroll: function() { },
            unlockScroll: function() { }
        };
        
        // Add more MudBlazor function stubs
        window.mudElementRef.saveFocus = function() { };
        window.mudElementRef.addOnBlurEvent = function() { };
        window.mudElementRef.restoreFocus = function() { };
        window.mudElementRef.focus = function() { };
        window.mudElementRef.select = function() { };
        window.mudElementRef.selectRange = function() { };
        
        // Elsa Component Bridge for React integration
        window.ElsaComponentBridge = {
            bridgeReference: null,
            
            initialize: function(dotNetRef) {
                this.bridgeReference = dotNetRef;
                console.log('Elsa Component Bridge initialized');
                
                // Notify parent window (React) that Elsa is ready
                if (window.parent !== window) {
                    window.parent.postMessage({ type: 'ELSA_READY' }, '*');
                }
            },
            
            renderWorkflowDesigner: function(containerId, workflowDefinitionId) {
                if (this.bridgeReference) {
                    return this.bridgeReference.invokeMethodAsync('renderWorkflowDesigner', containerId, workflowDefinitionId);
                }
                console.error('Bridge not initialized');
            },
            
            renderWorkflowInstances: function(containerId) {
                if (this.bridgeReference) {
                    return this.bridgeReference.invokeMethodAsync('renderWorkflowInstances', containerId);
                }
                console.error('Bridge not initialized');
            },
            
            renderWorkflowDashboard: function(containerId) {
                if (this.bridgeReference) {
                    return this.bridgeReference.invokeMethodAsync('renderWorkflowDashboard', containerId);
                }
                console.error('Bridge not initialized');
            },
            
            setAuthToken: function(token) {
                if (this.bridgeReference) {
                    return this.bridgeReference.invokeMethodAsync('setAuthToken', token);
                }
                console.error('Bridge not initialized');
            },
            
            disposeComponent: function(containerId) {
                if (this.bridgeReference) {
                    return this.bridgeReference.invokeMethodAsync('disposeComponent', containerId);
                }
                console.error('Bridge not initialized');
            }
        };
    </script>
</head>

<body>
    <div id="app">
        <svg class="loading-progress">
            <circle r="40%" cx="50%" cy="50%" />
            <circle r="40%" cx="50%" cy="50%" />
        </svg>
        <div class="loading-progress-text"></div>
    </div>

    <div id="blazor-error-ui">
        An unhandled error has occurred.
        <a href="" class="reload">Reload</a>
        <a class="dismiss">🗙</a>
    </div>
    <script src="_framework/blazor.webassembly.js"></script>
</body>

</html>
