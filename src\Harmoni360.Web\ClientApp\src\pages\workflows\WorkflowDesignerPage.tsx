import React from 'react';
import { useParams } from 'react-router-dom';
import { WorkflowDesignerComponent } from '../../components/workflows/WorkflowDesignerComponent';

const WorkflowDesignerPage: React.FC = () => {
  const { workflowId } = useParams<{ workflowId?: string }>();

  const handleReady = () => {
    console.log('Workflow Designer is ready');
  };

  const handleError = (error: Error) => {
    console.error('Workflow Designer error:', error);
  };

  return (
    <div className="workflow-designer-page">
      <div className="mb-4">
        <h2>Workflow Designer</h2>
        <p className="text-body-secondary">
          Design and configure your workflows using the visual designer.
        </p>
      </div>
      
      <WorkflowDesignerComponent 
        workflowDefinitionId={workflowId}
        className="w-full"
        onReady={handleReady}
        onError={handleError}
      />
    </div>
  );
};

export default WorkflowDesignerPage;