import React, { useEffect, useRef, useCallback } from 'react';
import { useAuth } from './useAuth';

// Type definitions for Elsa Component Bridge
declare global {
  interface Window {
    ElsaComponentBridge?: {
      renderWorkflowDesigner: (containerId: string, workflowDefinitionId?: string) => Promise<void>;
      renderWorkflowInstances: (containerId: string) => Promise<void>;
      renderWorkflowDashboard: (containerId: string) => Promise<void>;
      setAuthToken: (token: string) => Promise<void>;
      disposeComponent: (containerId: string) => Promise<void>;
    };
  }
}

export type ElsaComponentType = 'dashboard' | 'designer' | 'instances';

interface UseElsaComponentOptions {
  componentType: ElsaComponentType;
  containerId: string;
  workflowDefinitionId?: string;
  onReady?: () => void;
  onError?: (error: Error) => void;
}

interface ElsaComponentState {
  isLoading: boolean;
  isReady: boolean;
  error: string | null;
}

/**
 * React hook for integrating Elsa Studio Blazor components
 */
export const useElsaComponent = ({
  componentType,
  containerId,
  workflowDefinitionId,
  onReady,
  onError
}: UseElsaComponentOptions) => {
  const { token } = useAuth();
  const bridgeLoadedRef = useRef(false);
  const componentMountedRef = useRef(false);
  
  const [state, setState] = React.useState<ElsaComponentState>({
    isLoading: true,
    isReady: false,
    error: null
  });

  // Initialize Elsa Studio WASM if not already done
  const initializeElsaStudio = useCallback(async () => {
    if (bridgeLoadedRef.current) return;

    try {
      // Load Elsa Studio WASM if not already loaded
      if (!window.ElsaComponentBridge) {
        // Create hidden iframe to load Elsa Studio WASM
        const iframe = document.createElement('iframe');
        iframe.src = process.env.NODE_ENV === 'development' 
          ? 'http://localhost:6001' 
          : '/elsa-studio/';
        iframe.style.display = 'none';
        iframe.style.width = '0';
        iframe.style.height = '0';
        document.body.appendChild(iframe);

        // Wait for Elsa to be ready
        await new Promise<void>((resolve, reject) => {
          const timeout = setTimeout(() => {
            reject(new Error('Elsa Studio failed to load within timeout'));
          }, 30000);

          const handleMessage = (event: MessageEvent) => {
            if (event.data?.type === 'ELSA_READY') {
              clearTimeout(timeout);
              window.removeEventListener('message', handleMessage);
              // Copy the bridge from iframe to main window
              if (iframe.contentWindow?.ElsaComponentBridge) {
                window.ElsaComponentBridge = iframe.contentWindow.ElsaComponentBridge;
              }
              resolve();
            }
          };

          window.addEventListener('message', handleMessage);
        });
      }

      bridgeLoadedRef.current = true;
      
      // Set authentication token
      if (token && window.ElsaComponentBridge) {
        await window.ElsaComponentBridge.setAuthToken(token);
      }

      setState(prev => ({ ...prev, isLoading: false, isReady: true }));
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize Elsa Studio';
      setState(prev => ({ ...prev, isLoading: false, error: errorMessage }));
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    }
  }, [token, onError]);

  // Render the specified component
  const renderComponent = useCallback(async () => {
    if (!window.ElsaComponentBridge || !bridgeLoadedRef.current || componentMountedRef.current) {
      return;
    }

    try {
      setState(prev => ({ ...prev, isLoading: true, error: null }));

      switch (componentType) {
        case 'dashboard':
          await window.ElsaComponentBridge.renderWorkflowDashboard(containerId);
          break;
        case 'designer':
          await window.ElsaComponentBridge.renderWorkflowDesigner(containerId, workflowDefinitionId);
          break;
        case 'instances':
          await window.ElsaComponentBridge.renderWorkflowInstances(containerId);
          break;
        default:
          throw new Error(`Unknown component type: ${componentType}`);
      }

      componentMountedRef.current = true;
      setState(prev => ({ ...prev, isLoading: false }));
      onReady?.();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to render component';
      setState(prev => ({ ...prev, isLoading: false, error: errorMessage }));
      onError?.(error instanceof Error ? error : new Error(errorMessage));
    }
  }, [componentType, containerId, workflowDefinitionId, onReady, onError]);

  // Cleanup component
  const disposeComponent = useCallback(async () => {
    if (window.ElsaComponentBridge && componentMountedRef.current) {
      try {
        await window.ElsaComponentBridge.disposeComponent(containerId);
        componentMountedRef.current = false;
      } catch (error) {
        console.warn('Failed to dispose component:', error);
      }
    }
  }, [containerId]);

  // Initialize and render on mount
  useEffect(() => {
    const initialize = async () => {
      await initializeElsaStudio();
      if (bridgeLoadedRef.current) {
        await renderComponent();
      }
    };

    initialize();

    // Cleanup on unmount
    return () => {
      disposeComponent();
    };
  }, [initializeElsaStudio, renderComponent, disposeComponent]);

  // Update auth token when it changes
  useEffect(() => {
    if (token && window.ElsaComponentBridge && bridgeLoadedRef.current) {
      window.ElsaComponentBridge.setAuthToken(token).catch(console.error);
    }
  }, [token]);

  return {
    isLoading: state.isLoading,
    isReady: state.isReady,
    error: state.error,
    disposeComponent
  };
};