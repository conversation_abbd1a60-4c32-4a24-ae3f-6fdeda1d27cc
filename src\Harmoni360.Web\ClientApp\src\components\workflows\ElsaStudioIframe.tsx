import React, { useState, useEffect } from 'react';

interface ElsaStudioIframeProps {
  section?: 'dashboard' | 'designer' | 'instances' | 'monitoring';
  height?: string;
  className?: string;
  workflowDefinitionId?: string;
  fillContainer?: boolean;
}

export const ElsaStudioIframe: React.FC<ElsaStudioIframeProps> = ({
  section = 'dashboard',
  height = '800px',
  className = '',
  workflowDefinitionId,
  fillContainer = false
}) => {
  const [iframeUrl, setIframeUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    try {
      // Use the main backend for both development and production
      // In development: Elsa Studio runs on :6001, main backend on :5000
      // In production: Everything runs on the same domain
      const baseUrl = process.env.NODE_ENV === 'development' 
        ? 'http://localhost:6001' 
        : `${window.location.protocol}//${window.location.host}/elsa-studio`;
      
      // For Elsa Studio 3.x, we use the base URL and let the internal routing handle navigation
      // The Blazor WASM app will handle its own client-side routing
      setIframeUrl(baseUrl);
      setIsLoading(false);
    } catch (err) {
      setError('Failed to configure Elsa Studio URL');
      setIsLoading(false);
    }
  }, [section, workflowDefinitionId]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setError(null);
  };

  const handleIframeError = () => {
    const errorMsg = process.env.NODE_ENV === 'development' 
      ? 'Failed to load Elsa Studio. Please ensure the Elsa Studio application is running on port 6001.'
      : 'Failed to load Elsa Studio. Please check the server configuration.';
    setError(errorMsg);
    setIsLoading(false);
  };

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️ Connection Error</div>
          <div className="text-red-700 font-medium">{error}</div>
          <div className="text-sm text-red-600 mt-2">
            Make sure Elsa Studio is running: <code>dotnet run --project src/Harmoni360.ElsaStudio</code>
          </div>
        </div>
      </div>
    );
  }

  // Dynamic height calculation
  const dynamicStyle = fillContainer ? {
    height: '100%',
    minHeight: 'calc(100vh - 200px)' // Account for header/footer
  } : {
    height: height
  };

  return (
    <div 
      className={`relative ${fillContainer ? 'h-full' : ''} ${className}`}
      style={fillContainer ? { height: '100%' } : {}}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-50 z-10">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Workflow Designer...</p>
          </div>
        </div>
      )}
      
      <iframe
        src={iframeUrl}
        width="100%"
        style={dynamicStyle}
        frameBorder="0"
        sandbox="allow-scripts allow-forms allow-popups allow-top-navigation allow-same-origin"
        allow="fullscreen"
        loading="lazy"
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        title={`Elsa Studio - ${section}`}
        className="border-0 rounded-lg shadow-sm w-full"
      />
    </div>
  );
};

export default ElsaStudioIframe;