import React from 'react';
import { WorkflowInstancesComponent } from '../../components/workflows/WorkflowInstancesComponent';

const WorkflowInstancesPage: React.FC = () => {
  const handleReady = () => {
    console.log('Workflow Instances is ready');
  };

  const handleError = (error: Error) => {
    console.error('Workflow Instances error:', error);
  };

  return (
    <div className="workflow-instances-page">
      <div className="mb-4">
        <h2>Workflow Instances</h2>
        <p className="text-body-secondary">
          Monitor and manage running workflow instances.
        </p>
      </div>
      
      <WorkflowInstancesComponent 
        className="w-full"
        onReady={handleReady}
        onError={handleError}
      />
    </div>
  );
};

export default WorkflowInstancesPage;