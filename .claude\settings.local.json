{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(npx create-react-app:*)", "Bash(npx @coreui/coreui-react-app@latest:*)", "Bash(dotnet build)", "Bash(dotnet build:*)", "Bash(find:*)", "Bash(sudo rm:*)", "Bash(rm:*)", "Bash(npm install)", "Bash(npm start)", "Bash(ls:*)", "Bash(npm uninstall:*)", "Bash(npm install:*)", "Bash(npx tsc:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout 30s npm start)", "<PERSON><PERSON>(timeout 45s npm start)", "Bash(GENERATE_SOURCEMAP=false npm start)", "Bash(dotnet run:*)", "Bash(grep:*)", "Bash(export NODE_OPTIONS=\"--max_old_space_size=4096\")", "<PERSON><PERSON>(curl:*)", "Bash(dotnet clean:*)", "Bash(dotnet ef migrations add:*)", "Bash(dotnet ef database update:*)", "Bash(dotnet ef migrations:*)", "Bash(npm ls:*)", "Bash(npm run build:*)", "Bash(node:*)", "<PERSON><PERSON>(docker-compose ps:*)", "<PERSON><PERSON>(hostname:*)", "Bash(dotnet dev-certs:*)", "Bash(ss:*)", "Bash(cp:*)", "<PERSON><PERSON>(touch:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(true)", "<PERSON><PERSON>(killall:*)", "Bash(dotnet new:*)", "Bash(dotnet sln add:*)", "Bash(docker-compose logs:*)", "<PERSON><PERSON>(docker-compose up:*)", "Bash(docker logs:*)", "<PERSON><PERSON>(docker-compose down:*)", "Bash(docker volume rm:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(mv:*)", "WebFetch(domain:antblazor.com)", "Bash(dotnet restore)", "Bash(rg:*)", "<PERSON><PERSON>(dotnet nuget locals:*)", "Bash(dotnet restore:*)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(docker compose:*)", "Bash(dotnet add src/Harmoni360.Application reference src/Harmoni360.Domain)", "Bash(dotnet add:*)", "Bash(npm create:*)", "Bash(npx create-vite:*)", "Bash(npm init:*)", "Bash(dotnet sln remove:*)", "<PERSON><PERSON>(docker-compose restart:*)", "<PERSON><PERSON>(docker-compose exec:*)", "<PERSON><PERSON>(docker-compose:*)", "<PERSON><PERSON>(cat:*)", "WebFetch(domain:coreui.io)", "<PERSON><PERSON>(docker inspect:*)", "Bash(dotnet:*)", "Bash(npm cache clean:*)", "Bash(npm run dev:*)", "Bash(git add:*)", "Bash(npm run lint)", "Bash(npm audit:*)", "Bash(npm update:*)", "Bash(npm run type-check:*)", "Bash(npm run format:check:*)", "Bash(npm run test:*)", "Bash(npm run format:*)", "Bash(git restore:*)", "<PERSON><PERSON>(chmod:*)", "Bash(./test-ci-locally.sh:*)", "<PERSON><PERSON>(wget:*)", "Bash(tar:*)", "Bash(./act --version)", "Bash(sudo snap remove:*)", "Bash(firefox --version)", "Bash(psql:*)", "Bash(kill:*)", "<PERSON><PERSON>(sed:*)", "Bash(PGPASSWORD=postgres123 psql -h localhost -p 5432 -U postgres -d Harmoni360_Dev -f scripts/sql/apply-module-authorization-migration.sql)", "<PERSON><PERSON>(sudo apt-get:*)", "Bash(sudo apt-get install:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"private async Task Seed.*Async\" /mnt/d/Projects/harmoni-hse-360/src/Harmoni360.Infrastructure/Services/DataSeeder.cs)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"health.*seed|seed.*health\" /mnt/d/Projects/harmoni-hse-360/src/Harmoni360.Infrastructure/Services/DataSeeder.cs)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"Health\" /mnt/d/Projects/harmoni-hse-360/src/Harmoni360.Infrastructure/Services/DataSeeder.cs)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg \"DbSet<(\\w+)>\" /mnt/d/Projects/harmoni-hse-360/src/Harmoni360.Infrastructure/Persistence/ApplicationDbContext.cs -o --replace '$1')", "Bash(/mnt/d/Projects/harmoni-hse-360/replace-namespaces.sh:*)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"harmoni360\" --no-heading --line-number --with-filename)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"harmoni360\" --no-heading --line-number --with-filename --type cs --type csproj --type sln)", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"harmoni360\" --no-heading --line-number --with-filename -g \"*.cs\" -g \"*.csproj\" -g \"*.sln\")", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"harmoni360\" --no-heading --line-number --with-filename -g \"*.ts\" -g \"*.tsx\" -g \"*.json\")", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -i \"harmoni360\" --no-heading --line-number --with-filename -g \"*.md\" -g \"*.yml\" -g \"*.yaml\" -g \"Dockerfile*\" -g \"*.conf\" -g \"*.toml\")", "<PERSON><PERSON>(git mv:*)", "Bash(rsync:*)", "<PERSON><PERSON>(env)", "Bash(export PATH=\"$PATH:/root/.dotnet/tools\")", "Bash(/usr/local/lib/node_modules/@anthropic-ai/claude-code/vendor/ripgrep/x64-linux/rg -A 10 -B 10 \"Security|SecurityIncidentManagement\" \"/mnt/d/Projects/harmoni-hse-360/src/Harmoni360.Infrastructure/Services/DataSeeder.cs\")", "Bash(npx sass:*)", "Bash(~/.dotnet/tools/dotnet-ef migrations add AddManualPersonFieldsToInvolvedPersons -p src/Harmoni360.Infrastructure -s src/Harmoni360.Web)", "Bash(~/.dotnet/tools/dotnet-ef migrations list:*)", "Bash(~/.dotnet/tools/dotnet-ef migrations add AddHazardAuditLogsSupport --project src/Harmoni360.Infrastructure --startup-project src/Harmoni360.Web)", "Bash(~/.dotnet/tools/dotnet-ef database update:*)", "Bash(~/.dotnet/tools/dotnet-ef migrations remove:*)", "Bash(~/.dotnet/tools/dotnet-ef migrations add:*)", "Bash(~/.dotnet/tools/dotnet-ef:*)", "Bash(./test-seeding.sh:*)", "Bash(bash:*)", "Bash(export PATH=$PATH:/root/.dotnet/tools)", "Bash(/root/.dotnet/tools/dotnet-ef migrations add AddWorkPermitTables --project src/Harmoni360.Infrastructure --startup-project src/Harmoni360.Web)", "Bash(export PATH=/root/.dotnet/tools:$PATH)", "Bash(export:*)", "Bash(ASPNETCORE_ENVIRONMENT=Production dotnet run --urls=\"http://localhost:5001\")", "Bash(/home/<USER>/.dotnet/tools/dotnet-ef migrations add AddAuditEntities --project src/Harmoni360.Infrastructure --startup-project src/Harmoni360.Web --context ApplicationDbContext)", "Bash(pgrep:*)", "Bash(git commit:*)", "Bash(git push:*)", "Bash(npm run lint:*)", "Bash(npx vite:*)", "<PERSON><PERSON>(tsc:*)", "Bash(npm run typecheck:*)", "Bash(npm run:*)", "Bash(git checkout:*)", "Bash(git reset:*)", "Bash(git stash:*)"]}, "enableAllProjectMcpServers": false}