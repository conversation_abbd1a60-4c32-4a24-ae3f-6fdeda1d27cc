using Microsoft.EntityFrameworkCore;

namespace Harmoni360.Infrastructure.Persistence;

public partial class ApplicationDbContext
{
    partial void OnModelCreatingPartial(ModelBuilder modelBuilder)
    {
        // Note: Elsa v3.4.0 doesn't require manual ModelBuilder configuration
        // The workflow tables are configured automatically through the AddElsa() service registration
        // This approach uses <PERSON>'s automatic schema configuration instead of manual UseElsa() calls
    }
}