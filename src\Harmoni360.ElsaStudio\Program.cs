using Microsoft.AspNetCore.Components.Web;
using Microsoft.AspNetCore.Components.WebAssembly.Hosting;
using Microsoft.JSInterop;
using Elsa.Studio.Shell;
using Elsa.Studio.Core.BlazorWasm.Extensions;
using Elsa.Studio.Shell.Extensions;
using Elsa.Studio.Workflows.Extensions;
using Elsa.Studio.Login.BlazorWasm.Extensions;
using Elsa.Studio.Contracts;
using System.Reflection;
using System.Linq;
using Refit;
using Harmoni360.ElsaStudio.Services;

var builder = WebAssemblyHostBuilder.CreateDefault(args);

// Register root components
builder.RootComponents.Add<App>("#app");
builder.RootComponents.Add<HeadOutlet>("head::after");

// Configure HTTP client for Elsa API
var elsaApiUrl = "http://localhost:5000/elsa/api";
builder.Services.AddScoped(sp => new HttpClient 
{ 
    BaseAddress = new Uri(elsaApiUrl) 
});

// Register shell services and modules
builder.Services.AddCore();
builder.Services.AddShell();
builder.Services.AddLoginModule();
builder.Services.AddWorkflowsModule();

// Manually register the missing backend API client provider
builder.Services.AddScoped<IBackendApiClientProvider>(sp =>
{
    var httpClient = sp.GetRequiredService<HttpClient>();
    return new DefaultBackendApiClientProvider(httpClient, elsaApiUrl);
});

// Register the component bridge service
builder.Services.AddScoped<ElsaComponentBridge>();

var app = builder.Build();

// Make component bridge available to JavaScript
await app.Services.GetRequiredService<IJSRuntime>()
    .InvokeVoidAsync("window.ElsaComponentBridge.initialize", 
        DotNetObjectReference.Create(app.Services.GetRequiredService<ElsaComponentBridge>()));

await app.RunAsync();

// Refit-based backend API client provider
public class DefaultBackendApiClientProvider : IBackendApiClientProvider
{
    private readonly HttpClient _httpClient;
    private readonly Dictionary<Type, object> _cachedClients = new();
    
    public DefaultBackendApiClientProvider(HttpClient httpClient, string url)
    {
        _httpClient = httpClient;
        // Ensure URL doesn't end with slash to prevent double slash in requests
        Url = new Uri(url.TrimEnd('/'));
    }

    public Uri Url { get; }

    public ValueTask<T> GetApiAsync<T>(CancellationToken cancellationToken = default) where T : class
    {
        if (_cachedClients.TryGetValue(typeof(T), out var cached))
        {
            return ValueTask.FromResult((T)cached);
        }

        // Use Refit to create the API client
        var client = CreateRefitClient<T>();
        _cachedClients[typeof(T)] = client;
        
        return ValueTask.FromResult(client);
    }
    
    private T CreateRefitClient<T>() where T : class
    {
        try
        {
            // Try to use Refit's RestService.For method
            var refitSettings = new Refit.RefitSettings();
            var client = Refit.RestService.For<T>(_httpClient, refitSettings);
            return client;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to create Refit client for {typeof(T).Name}: {ex.Message}");
            
            // Fallback to mock implementation
            return CreateMockApi<T>();
        }
    }
    
    private static T CreateMockApi<T>() where T : class
    {
        // Create a simple proxy that returns empty/default results
        var proxyType = DispatchProxy.Create<T, MockApiProxy>();
        return proxyType;
    }
}

public class MockApiProxy : DispatchProxy
{
    protected override object? Invoke(MethodInfo? targetMethod, object?[]? args)
    {
        // Return appropriate default values based on return type
        if (targetMethod?.ReturnType == typeof(Task))
        {
            return Task.CompletedTask;
        }
        
        if (targetMethod?.ReturnType.IsGenericType == true)
        {
            var genericType = targetMethod.ReturnType.GetGenericTypeDefinition();
            if (genericType == typeof(Task<>))
            {
                var resultType = targetMethod.ReturnType.GetGenericArguments()[0];
                var defaultValue = GetDefaultValue(resultType);
                return Task.FromResult(defaultValue);
            }
            
            if (genericType == typeof(ValueTask<>))
            {
                var resultType = targetMethod.ReturnType.GetGenericArguments()[0];
                var defaultValue = GetDefaultValue(resultType);
                return ValueTask.FromResult(defaultValue);
            }
        }
        
        return GetDefaultValue(targetMethod?.ReturnType);
    }
    
    private static object? GetDefaultValue(Type? type)
    {
        if (type == null) return null;
        if (type.IsValueType) return Activator.CreateInstance(type);
        
        // For common collection types, return empty collections
        if (type.IsGenericType)
        {
            var genericDef = type.GetGenericTypeDefinition();
            if (genericDef == typeof(List<>))
            {
                return Activator.CreateInstance(type);
            }
            if (genericDef == typeof(IEnumerable<>) || genericDef == typeof(ICollection<>) || genericDef == typeof(IList<>))
            {
                var elementType = type.GetGenericArguments()[0];
                var listType = typeof(List<>).MakeGenericType(elementType);
                return Activator.CreateInstance(listType);
            }
        }
        
        return null;
    }
}
