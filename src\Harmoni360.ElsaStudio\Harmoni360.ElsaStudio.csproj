<Project Sdk="Microsoft.NET.Sdk.BlazorWebAssembly">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Elsa.Api.Client" Version="3.4.2" />
    <PackageReference Include="Elsa.Studio" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Core.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Login.BlazorWasm" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Shell" Version="3.4.0" />
    <PackageReference Include="Elsa.Studio.Workflows" Version="3.4.0" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly" Version="8.0.17" />
    <PackageReference Include="Microsoft.AspNetCore.Components.WebAssembly.DevServer" Version="8.0.17" PrivateAssets="all" />
  </ItemGroup>

</Project>
