# Multi-app deployment to Fly.io
name: Deploy to Fly.io

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  detect-changes:
    runs-on: ubuntu-latest
    outputs:
      main-app-changed: ${{ steps.changes.outputs.main-app }}
      elsa-studio-changed: ${{ steps.changes.outputs.elsa-studio }}
    steps:
      - uses: actions/checkout@v4
      - uses: dorny/paths-filter@v2
        id: changes
        with:
          filters: |
            main-app:
              - 'src/Harmoni360.Web/**'
              - 'src/Harmoni360.Application/**'
              - 'src/Harmoni360.Domain/**'
              - 'src/Harmoni360.Infrastructure/**'
            elsa-studio:
              - 'src/Harmoni360.ElsaStudio/**'

  deploy-main-app:
    needs: detect-changes
    if: needs.detect-changes.outputs.main-app-changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: flyctl deploy --config fly-main.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}

  deploy-elsa-studio:
    needs: detect-changes
    if: needs.detect-changes.outputs.elsa-studio-changed == 'true'
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: superfly/flyctl-actions/setup-flyctl@master
      - run: flyctl deploy --config fly-elsa-studio.toml
        env:
          FLY_API_TOKEN: ${{ secrets.FLY_API_TOKEN }}