import React, { useEffect, useRef, useState } from 'react';
import { useAuth } from '../../hooks/useAuth';

interface ElsaStudioDirectProps {
  height?: string;
  className?: string;
}

declare global {
  interface Window {
    Blazor?: {
      start: (options?: any) => Promise<void>;
      platform?: any;
    };
    DotNet?: {
      invokeMethodAsync: (assemblyName: string, methodName: string, ...args: any[]) => Promise<any>;
      createJSObjectReference: (obj: any) => any;
    };
  }
}

export const ElsaStudioDirect: React.FC<ElsaStudioDirectProps> = ({
  height = 'calc(100vh - 120px)',
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [blazorLoaded, setBlazorLoaded] = useState(false);
  const { token } = useAuth();

  useEffect(() => {
    const loadElsaStudio = async () => {
      try {
        setIsLoading(true);
        setError(null);

        // Create the app div that Blazor will render into
        if (containerRef.current) {
          containerRef.current.innerHTML = '<div id="elsa-app">Loading Elsa Studio...</div>';
        }

        // Load Blazor WebAssembly framework
        const blazorScript = document.createElement('script');
        blazorScript.src = '/elsa-studio/_framework/blazor.webassembly.js';
        blazorScript.async = true;

        blazorScript.onload = async () => {
          try {
            // Wait for Blazor to be available
            const waitForBlazor = () => {
              return new Promise<void>((resolve) => {
                const checkBlazor = () => {
                  if (window.Blazor) {
                    resolve();
                  } else {
                    setTimeout(checkBlazor, 50);
                  }
                };
                checkBlazor();
              });
            };

            await waitForBlazor();

            // Start Blazor with our configuration
            await window.Blazor!.start({
              applicationParameters: {
                apiBaseUrl: `${window.location.origin}/elsa/api`
              }
            });

            setBlazorLoaded(true);
            setIsLoading(false);

            // Set authentication token if available
            if (token && window.DotNet) {
              try {
                await window.DotNet.invokeMethodAsync(
                  'Harmoni360.ElsaStudio', 
                  'SetAuthToken', 
                  token
                );
              } catch (err) {
                console.warn('Failed to set auth token:', err);
              }
            }

          } catch (err) {
            console.error('Failed to start Blazor:', err);
            setError('Failed to initialize Elsa Studio WASM runtime');
            setIsLoading(false);
          }
        };

        blazorScript.onerror = () => {
          setError('Failed to load Elsa Studio WASM files. Please check server configuration.');
          setIsLoading(false);
        };

        // Only add script if not already loaded
        if (!document.querySelector('script[src="/elsa-studio/_framework/blazor.webassembly.js"]')) {
          document.head.appendChild(blazorScript);
        } else if (window.Blazor) {
          // Already loaded
          setBlazorLoaded(true);
          setIsLoading(false);
        }

      } catch (err) {
        console.error('Error loading Elsa Studio:', err);
        setError('Failed to load Elsa Studio');
        setIsLoading(false);
      }
    };

    loadElsaStudio();
  }, []);

  // Update auth token when it changes
  useEffect(() => {
    if (blazorLoaded && token && window.DotNet) {
      window.DotNet.invokeMethodAsync('Harmoni360.ElsaStudio', 'SetAuthToken', token)
        .catch(err => console.warn('Failed to update auth token:', err));
    }
  }, [token, blazorLoaded]);

  if (error) {
    return (
      <div className="flex items-center justify-center h-96 bg-red-50 border border-red-200 rounded-lg">
        <div className="text-center">
          <div className="text-red-600 mb-2">⚠️ Elsa Studio Error</div>
          <div className="text-red-700 font-medium">{error}</div>
          <div className="text-sm text-red-600 mt-2">
            Please ensure Elsa Studio WASM files are properly deployed.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`elsa-studio-direct ${className}`}>
      {isLoading && (
        <div className="flex items-center justify-center h-96 bg-gray-50">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading Elsa Studio WASM...</p>
            <p className="mt-2 text-sm text-gray-500">This may take a moment on first load</p>
          </div>
        </div>
      )}
      
      <div 
        ref={containerRef}
        style={{ 
          height,
          width: '100%',
          display: isLoading ? 'none' : 'block'
        }}
        className="elsa-studio-container"
      />
    </div>
  );
};

export default ElsaStudioDirect;